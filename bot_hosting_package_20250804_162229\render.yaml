services:
  - type: web
    name: minecraft-mods-bot
    env: python
    plan: free
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
      python render_network_fix.py
    startCommand: python render_start.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: RENDER
        value: true
      - key: RENDER_SERVICE_TYPE
        value: web
      - key: PYTHONUNBUFFERED
        value: 1
      - key: PYTHONIOENCODING
        value: utf-8
      - key: BOT_TOKEN
        value: **********************************************
      - key: TELEGRAM_BOT_TOKEN
        value: **********************************************
      - key: ADMIN_CHAT_ID
        value: 7513880877
      - key: SUPABASE_URL
        value: https://ytqxxodyecdeosnqoure.supabase.co
      - key: SUPABASE_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4
      - key: USE_NGROK
        value: false
      - key: NGROK_ENABLED
        value: false
      - key: WEB_SERVER_URL
        value: https://1c547fe5.sendaddons.pages.dev
      - key: WEB_SERVER_PORT
        value: 5000
      - key: TELEGRAM_WEB_APP_PORT
        value: 5001
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: LOG_LEVEL
        value: INFO
